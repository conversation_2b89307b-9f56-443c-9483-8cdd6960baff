# 
# 注意：这是一个模板文件，不要直接使用
# 
# 基础镜像将由构建脚本替换
ARG BASE_IMAGE
FROM $BASE_IMAGE

# 显示基础镜像的环境变量（用于调试和验证继承）
RUN echo "=== 基础镜像环境变量 ===" && \
    env | sort && \
    echo "=== JAVA 相关环境变量 ===" && \
    env | grep -E "(JAVA|JDK|JRE)" || echo "未找到 JAVA 相关环境变量" && \
    echo "=========================="

# 动态检测和设置 JAVA_HOME
RUN set -eux; \
    echo "=== 检测 Java 安装 ==="; \
    # 首先尝试从基础镜像继承的 JAVA_HOME
    if [ -n "${JAVA_HOME:-}" ] && [ -d "${JAVA_HOME:-}" ] && [ -f "${JAVA_HOME}/bin/java" ]; then \
        echo "使用基础镜像的 JAVA_HOME: $JAVA_HOME"; \
        DETECTED_JAVA_HOME="$JAVA_HOME"; \
    # 如果没有，尝试常见的 JDK 路径
    elif [ -d "/usr/local/jdk1.8.0_172" ] && [ -f "/usr/local/jdk1.8.0_172/bin/java" ]; then \
        echo "检测到 JDK 8 路径: /usr/local/jdk1.8.0_172"; \
        DETECTED_JAVA_HOME="/usr/local/jdk1.8.0_172"; \
    elif [ -d "/usr/lib/jvm/java-8-openjdk-amd64" ] && [ -f "/usr/lib/jvm/java-8-openjdk-amd64/bin/java" ]; then \
        echo "检测到 OpenJDK 8 路径: /usr/lib/jvm/java-8-openjdk-amd64"; \
        DETECTED_JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"; \
    elif [ -d "/usr/lib/jvm/java-17-openjdk-amd64" ] && [ -f "/usr/lib/jvm/java-17-openjdk-amd64/bin/java" ]; then \
        echo "检测到 OpenJDK 17 路径: /usr/lib/jvm/java-17-openjdk-amd64"; \
        DETECTED_JAVA_HOME="/usr/lib/jvm/java-17-openjdk-amd64"; \
    else \
        echo "错误: 无法找到有效的 Java 安装"; \
        echo "尝试查找所有可能的 Java 安装:"; \
        find /usr -name "java" -type f -executable 2>/dev/null || true; \
        find /opt -name "java" -type f -executable 2>/dev/null || true; \
        exit 1; \
    fi; \
    echo "最终使用的 JAVA_HOME: $DETECTED_JAVA_HOME"; \
    # 确保 JDK 目录权限正确
    chown -R root:root "$DETECTED_JAVA_HOME"; \
    chmod -R a+rX "$DETECTED_JAVA_HOME"; \
    chmod +x "$DETECTED_JAVA_HOME/bin/"*; \
    # 创建标准的 java 符号链接
    ln -sf "$DETECTED_JAVA_HOME/bin/java" /usr/local/bin/java; \
    ln -sf "$DETECTED_JAVA_HOME/bin/javac" /usr/local/bin/javac; \
    # 写入环境变量到系统配置文件
    echo "export JAVA_HOME=$DETECTED_JAVA_HOME" >> /etc/bash.bashrc; \
    echo "export PATH=$DETECTED_JAVA_HOME/bin:/usr/local/tomcat/bin:\$PATH" >> /etc/bash.bashrc; \
    echo "JAVA_HOME=$DETECTED_JAVA_HOME" >> /etc/environment; \
    echo "PATH=$DETECTED_JAVA_HOME/bin:/usr/local/tomcat/bin:\$PATH" >> /etc/environment; \
    # 保存检测到的 JAVA_HOME 供后续使用
    echo "$DETECTED_JAVA_HOME" > /tmp/detected_java_home; \
    echo "=== Java 检测完成 ==="

# 设置环境变量
ENV JAVA_TOOL_OPTIONS=${JAVA_TOOL_OPTIONS:-} \
    TOMCAT_VERSION=$TOMCAT_VERSION \
    TOMCAT_MAJOR=$TOMCAT_MAJOR \
    TOMCAT_VARIANT=$TOMCAT_VARIANT \
    TOMCAT_HOME=/usr/local/tomcat \
    CATALINA_HOME=/usr/local/tomcat \
    CATALINA_BASE=/usr/local/tomcat \
    TOMCAT_NATIVE_LIBDIR=/usr/local/tomcat/native-jni-lib \
    LD_LIBRARY_PATH=/usr/lib64:/usr/local/tomcat/native-jni-lib:${LD_LIBRARY_PATH:-} \
    CRYPTO_FILES_VERSION=$CRYPTO_FILES_VERSION

# 动态设置 JAVA_HOME 和 PATH 环境变量
RUN DETECTED_JAVA_HOME=$(cat /tmp/detected_java_home) && \
    echo "export JAVA_HOME=$DETECTED_JAVA_HOME" > /etc/profile.d/java.sh && \
    echo "export PATH=$DETECTED_JAVA_HOME/bin:/usr/local/tomcat/bin:\$PATH" >> /etc/profile.d/java.sh && \
    chmod +x /etc/profile.d/java.sh

ENV JAVA_HOME=""
ENV PATH="/usr/local/tomcat/bin:$PATH"

# 在运行时设置正确的 JAVA_HOME
RUN DETECTED_JAVA_HOME=$(cat /tmp/detected_java_home) && \
    echo "JAVA_HOME=$DETECTED_JAVA_HOME" > /etc/default/java && \
    echo "PATH=$DETECTED_JAVA_HOME/bin:/usr/local/tomcat/bin:\$PATH" >> /etc/default/java

# 验证环境变量和 Java 安装
RUN set -eux; \
    DETECTED_JAVA_HOME=$(cat /tmp/detected_java_home); \
    echo "=== Tomcat 镜像环境变量 ==="; \
    echo "检测到的 JAVA_HOME: $DETECTED_JAVA_HOME"; \
    echo "当前 JAVA_HOME: $JAVA_HOME"; \
    echo "PATH: $PATH"; \
    echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"; \
    echo "JAVA_TOOL_OPTIONS: $JAVA_TOOL_OPTIONS"; \
    echo "=== Java 可执行文件检查 ==="; \
    if [ -f "$DETECTED_JAVA_HOME/bin/java" ]; then \
        ls -la "$DETECTED_JAVA_HOME/bin/java"; \
        echo "✓ Java 可执行文件存在"; \
    else \
        echo "错误: $DETECTED_JAVA_HOME/bin/java 不存在"; \
        exit 1; \
    fi; \
    echo "=== which java 检查 ==="; \
    which java && echo "✓ java 命令在 PATH 中找到" || echo "警告: java 命令未在 PATH 中找到"; \
    echo "=== Java 版本测试 ==="; \
    "$DETECTED_JAVA_HOME/bin/java" -version 2>&1 && echo "✓ Java 版本检查成功" || (echo "错误: java -version 失败"; exit 1); \
    echo "=== bash 环境中的 Java 测试 ==="; \
    bash -c "source /etc/bash.bashrc && echo 'JAVA_HOME in bash: \$JAVA_HOME' && \$JAVA_HOME/bin/java -version" 2>&1 && echo "✓ bash 环境中 Java 正常" || echo "警告: bash 环境中 Java 可能有问题"; \
    echo "=========================="

# 设置工作目录
WORKDIR $CATALINA_HOME

# 设置SHA512校验和（由构建脚本替换）
ENV TOMCAT_SHA512=$TOMCAT_SHA512

# 复制预下载的Tomcat包
COPY docker/infrastructure/tomcat/resources/apache-tomcat-${TOMCAT_VERSION}.tar.gz tomcat.tar.gz

# 验证校验和并解压
RUN set -eux; \
    if [ -n "$TOMCAT_SHA512" ]; then \
        echo "$TOMCAT_SHA512 *tomcat.tar.gz" | sha512sum --strict --check -; \
    fi; \
    tar -xf tomcat.tar.gz --strip-components=1; \
    rm tomcat.tar.gz; \
    \
    # 添加可执行权限
    chmod +x bin/*.sh; \
    \
    # 简单验证安装 - 只检查关键文件是否存在
    echo "验证Tomcat安装..."; \
    if [ ! -f "$CATALINA_HOME/bin/catalina.sh" ] || \
       [ ! -f "$CATALINA_HOME/conf/server.xml" ] || \
       [ ! -f "$CATALINA_HOME/lib/catalina.jar" ] || \
       [ ! -d "$CATALINA_HOME/webapps" ]; then \
        echo "Tomcat安装不完整，缺少关键文件"; \
        exit 1; \
    fi; \
    echo "Tomcat安装验证通过"

# 创建tomcat用户
RUN set -eux; \
    groupadd -r tomcat --gid=1000; \
    useradd -r -g tomcat --uid=1000 --home-dir=/usr/local/tomcat --shell=/bin/bash tomcat

# Copy NetcaJCrypto jar files (only for crypto variant)
COPY docker/infrastructure/tomcat/shared/crypto-files/slf4j* $CATALINA_HOME/lib/
COPY docker/infrastructure/tomcat/shared/crypto-files/NetcaJCrypto* $CATALINA_HOME/lib/

# 仅复制自定义的server.xml配置文件
COPY docker/infrastructure/tomcat/shared/conf/server.xml $CATALINA_HOME/conf/server.xml

# 复制启动脚本
COPY docker/infrastructure/tomcat/shared/scripts/entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# 设置所有权和权限
RUN set -eux; \
    DETECTED_JAVA_HOME=$(cat /tmp/detected_java_home); \
    # 确保所有文件属于tomcat用户
    chown -R tomcat:tomcat /usr/local/tomcat; \
    # 设置目录权限：所有者可读写执行，组和其他用户可读执行
    find /usr/local/tomcat -type d -exec chmod 755 {} \;; \
    # 设置文件权限：所有者可读写，组和其他用户可读，脚本文件可执行
    find /usr/local/tomcat -type f -exec chmod 644 {} \;; \
    # 确保所有.sh脚本文件可执行
    find /usr/local/tomcat -name "*.sh" -exec chmod 755 {} \;; \
    # 设置特殊目录权限（临时目录需要写权限）
    chmod 1777 logs temp work; \
    # 修复 JDK 目录权限，确保 tomcat 用户可以访问
    if [ -d "$DETECTED_JAVA_HOME" ]; then \
        echo "修复 JDK 目录权限: $DETECTED_JAVA_HOME"; \
        chmod -R a+rX "$DETECTED_JAVA_HOME"; \
        # 确保 tomcat 用户可以访问 JDK
        chgrp -R tomcat "$DETECTED_JAVA_HOME" 2>/dev/null || true; \
        ls -la "$DETECTED_JAVA_HOME/bin/java"; \
        echo "测试 tomcat 用户访问 Java:"; \
        su tomcat -c "export JAVA_HOME=$DETECTED_JAVA_HOME && \$JAVA_HOME/bin/java -version" 2>&1 || echo "警告: tomcat 用户无法运行 java"; \
    else \
        echo "警告: JAVA_HOME 目录不存在: $DETECTED_JAVA_HOME"; \
    fi; \
    # 最终验证 setclasspath.sh 脚本
    if [ -f "/usr/local/tomcat/bin/setclasspath.sh" ]; then \
        echo "测试 setclasspath.sh 脚本:"; \
        su tomcat -c "export JAVA_HOME=$DETECTED_JAVA_HOME && cd /usr/local/tomcat && bash ./bin/setclasspath.sh" 2>&1 || echo "警告: setclasspath.sh 脚本测试失败"; \
    fi; \
    # 清理临时文件
    rm -f /tmp/detected_java_home

# 添加标签信息
LABEL maintainer="Medical Products Team" \
      description="Tomcat $TOMCAT_VERSION server with $JDK_VERSION for medical products" \
      version="$TOMCAT_VERSION" \
      build.date="$BUILD_DATE" \
      vendor="BTIT" \
      architecture="$ARCH" \
      base.image="$BASE_IMAGE" \
      java.type="$JAVA_TYPE" \
      java.distribution="$JAVA_DISTRIBUTION" \
      os.codename="$OS_CODENAME"

# 暴露端口
EXPOSE 8080 443 8443 8005 8009

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost:8080/ || exit 1

# 启动Tomcat
USER tomcat
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["/usr/local/tomcat/bin/catalina.sh", "run"]

