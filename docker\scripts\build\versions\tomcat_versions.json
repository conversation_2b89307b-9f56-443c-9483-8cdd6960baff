{"default_version": "9.0.100", "build_defaults": {"namespace": "btit", "repository": "infra/tomcat", "registry": "192.168.200.39:1443", "auto_fetch_sha512": true, "platforms": ["linux/amd64", "linux/arm64"]}, "jdk_base_images": {"8": "192.168.200.39:1443/btit/infra/jdk:8u172-b01-jdk", "17": "192.168.200.39:1443/btit/infra/jdk:17u0-b35-jdk-jammy-crypto-sc34"}, "jdk_configurations": {"8": {"java_type": "jdk", "java_distribution": "btit", "os_suffix": "crypto-sc34"}, "17": {"java_type": "jdk", "java_distribution": "btit", "os_suffix": "crypto-sc34"}}, "os_codename": "jammy", "versions": {"9.0.100": {"download_url": "https://archive.apache.org/dist/tomcat/tomcat-9/v9.0.100/bin/apache-tomcat-9.0.100.tar.gz"}, "10.1.17": {"download_url": "https://archive.apache.org/dist/tomcat/tomcat-10/v10.1.17/bin/apache-tomcat-10.1.17.tar.gz"}, "8.5.98": {"download_url": "https://archive.apache.org/dist/tomcat/tomcat-8/v8.5.98/bin/apache-tomcat-8.5.98.tar.gz"}}, "jdk_compatibility": {"8.5": ["8"], "9.0": ["8", "17"], "10.1": ["17"]}, "_comments": {"default_version": "默认使用的Tomcat版本", "build_defaults": "构建默认配置", "jdk_base_images": "完整的JDK基础镜像标签", "jdk_configurations": "JDK配置信息，按JDK版本分组", "versions": "支持的Tomcat版本配置", "versions.download_url": "Tomcat安装包下载地址", "jdk_compatibility": "Tomcat版本与JDK版本的兼容性映射"}}