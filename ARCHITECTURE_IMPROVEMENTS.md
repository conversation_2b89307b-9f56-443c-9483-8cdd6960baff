# Tomcat 构建脚本架构检测改进

## 概述

对 `docker/scripts/build/build-tomcat.sh` 脚本进行了架构检测功能的改进，使其能够根据宿主机的CPU架构自动匹配，同时保持用户手动选择架构的能力。

## 改进内容

### 1. 增强架构检测函数 (`docker/scripts/lib/utils.sh`)

**改进前：**
- 仅支持 `x86_64` 和 `aarch64` 两种架构
- 检测逻辑简单
- 无调试信息

**改进后：**
- 支持更多架构变体：`x86_64|amd64` → `amd64`，`aarch64|arm64` → `arm64`
- 增加对不支持架构的友好提示（如32位架构）
- 添加详细的调试日志信息
- 修复了日志输出影响返回值的问题

### 2. 改进命令行参数处理

**新增功能：**
- 添加 `--list-archs` 选项，显示支持的架构列表和当前系统架构
- 改进帮助信息，明确说明架构自动检测功能
- 实现早期参数解析，使 `--help` 和 `--list-archs` 不依赖配置文件

### 3. 增强用户体验

**改进内容：**
- 在架构自动检测时提供清晰的日志信息
- 显示实际使用的架构而不是"系统默认"
- 对不支持的架构提供更友好的错误信息和解决建议

## 使用方法

### 查看支持的架构
```bash
bash docker/scripts/build/build-tomcat.sh --list-archs
```

### 自动检测架构（推荐）
```bash
bash docker/scripts/build/build-tomcat.sh -t 9.0.100 -j 8
```

### 手动指定架构
```bash
bash docker/scripts/build/build-tomcat.sh -t 9.0.100 -j 8 --arch amd64
bash docker/scripts/build/build-tomcat.sh -t 9.0.100 -j 8 --arch arm64
```

## 支持的架构

- **amd64**: Intel/AMD 64位架构（x86_64）
- **arm64**: ARM 64位架构（aarch64）

## 架构检测逻辑

1. **自动检测**：如果用户未指定 `--arch` 参数，脚本会自动检测宿主机架构
2. **兼容性检查**：验证检测到的或指定的架构是否在支持列表中
3. **错误处理**：对不支持的架构提供清晰的错误信息和解决建议

## 日志输出示例

```
[INFO] 未指定架构参数，正在自动检测宿主机架构...
[DEBUG] 检测到的原始系统架构: x86_64
[INFO] 自动检测到架构: amd64
```

## 向后兼容性

- 保持所有现有功能不变
- 现有的 `--arch` 参数继续工作
- 默认行为改进但不破坏现有脚本

## 技术细节

### 修改的文件
1. `docker/scripts/lib/utils.sh` - 增强 `get_system_arch()` 函数
2. `docker/scripts/build/build-tomcat.sh` - 改进参数处理和用户体验

### 关键改进点
- 日志输出重定向到 stderr 避免影响函数返回值
- 早期参数解析支持无配置文件的选项
- 详细的架构检测和验证逻辑
- 用户友好的错误信息和帮助文档
