#!/bin/bash
# ===============================================================
# 工具函数库
# 提供通用的工具函数
# ===============================================================

# 确保只加载一次
[[ -n "${_UTILS_SH_LOADED:-}" ]] && return 0
readonly _UTILS_SH_LOADED=1

# 导入日志库
source "$(dirname "${BASH_SOURCE[0]}")/logger.sh"

# 检查必需的命令是否存在
check_required_commands() {
    local -a commands=("$@")
    local missing_commands=()
    
    for cmd in "${commands[@]}"; do
        if ! command -v "$cmd" &>/dev/null; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "缺少必需的命令: ${missing_commands[*]}"
        log_error "请安装这些命令后再继续"
        return 1
    fi
    
    return 0
}

# 检查文件是否存在且可读
check_file_readable() {
    local file="$1"
    if [[ ! -f "$file" ]]; then
        log_error "文件不存在: $file"
        return 1
    fi
    if [[ ! -r "$file" ]]; then
        log_error "文件不可读: $file"
        return 1
    fi
    return 0
}

# 检查目录是否存在且可写
check_dir_writable() {
    local dir="$1"
    if [[ ! -d "$dir" ]]; then
        log_error "目录不存在: $dir"
        return 1
    fi
    if [[ ! -w "$dir" ]]; then
        log_error "目录不可写: $dir"
        return 1
    fi
    return 0
}

# 检查JSON文件是否有效
validate_json_file() {
    local file="$1"
    if ! check_file_readable "$file"; then
        return 1
    fi
    
    if ! jq . "$file" >/dev/null 2>&1; then
        log_error "无效的JSON文件: $file"
        return 1
    fi
    
    return 0
}

# 安全地创建目录
safe_mkdir() {
    local dir="$1"
    if [[ ! -d "$dir" ]]; then
        if ! mkdir -p "$dir" 2>/dev/null; then
            log_error "无法创建目录: $dir"
            return 1
        fi
    fi
    return 0
}

# 获取系统架构
get_system_arch() {
    local arch
    arch=$(uname -m)

    # 记录原始架构信息用于调试（输出到stderr避免影响返回值）
    log_debug "检测到的原始系统架构: $arch" >&2

    case "$arch" in
        x86_64|amd64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        i386|i686)
            log_warn "检测到32位x86架构($arch)，但Docker通常需要64位架构" >&2
            echo "unsupported"
            ;;
        armv7l|armv6l)
            log_warn "检测到ARM 32位架构($arch)，但当前仅支持ARM 64位" >&2
            echo "unsupported"
            ;;
        *)
            log_warn "未知的系统架构: $arch" >&2
            echo "unsupported"
            ;;
    esac
}

# 检查版本号格式是否有效
validate_version() {
    local version="$1"
    if [[ ! "$version" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_error "无效的版本号格式: $version"
        return 1
    fi
    return 0
}

# 计算文件的SHA512校验和
calculate_sha512() {
    local file="$1"
    if ! check_file_readable "$file"; then
        return 1
    fi
    
    local sha512
    if command -v sha512sum &>/dev/null; then
        sha512=$(sha512sum "$file" | cut -d' ' -f1)
    elif command -v shasum &>/dev/null; then
        sha512=$(shasum -a 512 "$file" | cut -d' ' -f1)
    else
        log_error "未找到计算SHA512的工具"
        return 1
    fi
    
    echo "$sha512"
    return 0
}

# 检查网络连接
check_network() {
    local host="$1"
    local port="${2:-80}"
    local timeout="${3:-5}"
    
    if command -v nc &>/dev/null; then
        if nc -z -w "$timeout" "$host" "$port" &>/dev/null; then
            return 0
        fi
    elif command -v curl &>/dev/null; then
        if curl -s --connect-timeout "$timeout" "http://${host}:${port}" &>/dev/null; then
            return 0
        fi
    else
        log_warn "未找到网络检查工具(nc/curl)"
        # 使用简单的ping测试
        ping -n 1 -w "$((timeout * 1000))" "$host" &>/dev/null
        return $?
    fi
    
    return 1
}

# 检查Docker守护进程是否运行
check_docker_daemon() {
    if ! docker info &>/dev/null; then
        log_error "Docker守护进程未运行"
        return 1
    fi
    return 0
}

# 检查Harbor仓库是否可访问
check_harbor_registry() {
    local registry="$1"
    local timeout="${2:-5}"
    
    # 移除协议前缀
    registry="${registry#http://}"
    registry="${registry#https://}"
    
    # 提取主机名和端口
    local host port
    if [[ "$registry" =~ : ]]; then
        host="${registry%:*}"
        port="${registry#*:}"
    else
        host="$registry"
        port="443"
    fi
    
    if ! check_network "$host" "$port" "$timeout"; then
        log_error "无法连接到Harbor仓库: $registry"
        return 1
    fi
    
    return 0
}

# 导出公共函数
export -f check_required_commands
export -f check_file_readable
export -f check_dir_writable
export -f validate_json_file
export -f safe_mkdir
export -f get_system_arch
export -f validate_version
export -f calculate_sha512
export -f check_network
export -f check_docker_daemon
export -f check_harbor_registry 