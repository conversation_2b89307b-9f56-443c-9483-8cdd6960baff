# JDK构建脚本架构自动检测功能

## 功能概述

JDK构建脚本现在支持智能架构选择，能够：
1. **自动检测宿主机架构**并构建对应的镜像（默认行为）
2. **允许用户手动指定**目标架构
3. **支持多架构构建**（同时构建多个架构）

## 新增功能

### 1. 宿主机架构自动检测

```bash
# 检测函数
detect_host_arch() {
    local host_arch=$(uname -m)
    case $host_arch in
        "x86_64")  echo "amd64" ;;
        "aarch64") echo "arm64" ;;
        "arm64")   echo "arm64" ;;
        *)         echo "amd64" ;;  # 默认
    esac
}
```

### 2. 新增命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--arch=<架构>` | 指定单一目标架构 | `--arch=arm64` |
| `--all-arch` | 构建所有支持的架构 | `--all-arch` |

### 3. 智能架构选择逻辑

```bash
# 优先级顺序：
1. --arch=<架构> 参数（最高优先级）
2. --all-arch 参数
3. 宿主机架构自动检测（默认）
```

## 使用示例

### 自动检测模式（推荐）

```bash
# 自动检测宿主机架构并构建
./build-jdk.sh 8

# 输出示例：
# 自动检测到宿主机架构: amd64
# 开始构建 JDK 8 (jammy) for amd64
```

### 手动指定架构

```bash
# 构建特定架构
./build-jdk.sh --arch=arm64 8

# 构建所有架构
./build-jdk.sh --all-arch 8
```

### 结合其他参数

```bash
# 指定架构 + 调试模式
./build-jdk.sh --arch=arm64 --debug 8

# 所有架构 + 输出目录
./build-jdk.sh --all-arch --output-dir=/tmp/dockerfiles 8
```

## 架构映射关系

| 宿主机架构 (uname -m) | Docker架构 | 目录名称 | 说明 |
|---------------------|-----------|---------|------|
| x86_64 | amd64 | x86_64 | Intel/AMD 64位 |
| aarch64 | arm64 | aarch64 | ARM 64位 |
| arm64 | arm64 | aarch64 | ARM 64位（macOS） |

## 构建流程改进

### 1. 架构验证

```bash
# 验证用户指定的架构是否在支持列表中
for user_arch in "${ARCHITECTURES[@]}"; do
    if [[ " ${supported_archs[*]} " =~ " ${user_arch} " ]]; then
        build_archs+=("$user_arch")
    else
        echo "警告: 架构 $user_arch 不在支持列表中，跳过"
    fi
done
```

### 2. 智能默认选择

```bash
# 如果用户没有指定架构，使用宿主机架构
if [ ${#ARCHITECTURES[@]} -eq 0 ]; then
    local host_arch=$(detect_host_arch)
    if [[ " ${supported_archs[*]} " =~ " ${host_arch} " ]]; then
        build_archs=("$host_arch")
        echo "自动检测到宿主机架构: $host_arch"
    else
        echo "警告: 宿主机架构不支持，使用默认架构"
        build_archs=("${supported_archs[0]}")
    fi
fi
```

### 3. 详细构建信息

```bash
构建信息:
  基础镜像: 来自OS构建的Ubuntu镜像
  目标Harbor仓库: registry.example.com:1443
  宿主机架构: amd64
  构建模式: 自动检测宿主机架构
  检测结果: amd64
  JDK版本: 8
    - JDK 8: 详细版本 1.8.0_172, 支持架构: amd64 arm64
```

## 兼容性

### 向后兼容

- 现有的构建命令无需修改
- 默认行为从"构建amd64"改为"自动检测宿主机架构"
- 所有原有参数保持不变

### 新功能

- 新增的 `--arch` 和 `--all-arch` 参数
- 自动架构检测功能
- 更详细的构建信息输出

## 错误处理

### 常见场景

1. **宿主机架构不支持**
   ```
   警告: 宿主机架构 arm64 不在支持列表中，使用默认架构
   ```

2. **用户指定架构不支持**
   ```
   警告: 架构 arm64 不在JDK 8 的支持列表中，跳过
   ```

3. **没有可构建的架构**
   ```
   错误: 没有可构建的架构，跳过JDK 8
   ```

## 测试验证

### 1. 运行测试脚本

```bash
./test-arch-support.sh
```

### 2. 查看示例

```bash
./examples/arch-build-examples.sh
```

### 3. 验证帮助信息

```bash
./build-jdk.sh --help
```

## 配置文件支持

在 `jdk_versions.json` 中配置支持的架构：

```json
{
  "versions": {
    "8": {
      "jdk_version": "1.8.0_172",
      "supported_architectures": ["amd64", "arm64"]
    }
  }
}
```

## 最佳实践

1. **推荐使用自动检测**：让脚本自动选择合适的架构
2. **CI/CD环境**：使用 `--arch` 参数明确指定目标架构
3. **多架构发布**：使用 `--all-arch` 参数构建所有架构
4. **调试问题**：使用 `--debug` 参数保留临时文件

## 总结

这次更新使JDK构建脚本更加智能和用户友好：
- ✅ 自动检测宿主机架构
- ✅ 支持手动架构选择
- ✅ 支持多架构构建
- ✅ 保持向后兼容
- ✅ 提供详细的构建信息
- ✅ 完善的错误处理
