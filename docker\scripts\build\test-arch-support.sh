#!/bin/bash

# 测试架构支持的脚本
# 用于验证JDK构建脚本的架构区分功能

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# 引入架构转换函数
source "${SCRIPT_DIR}/build-jdk.sh"

echo "=== 测试架构转换函数 ==="

# 测试架构转换
test_arch_conversion() {
    local docker_arch=$1
    local expected_dir_arch=$2

    local result=$(docker_arch_to_dir_arch "$docker_arch")
    if [ "$result" = "$expected_dir_arch" ]; then
        echo "✓ $docker_arch -> $result (正确)"
    else
        echo "✗ $docker_arch -> $result (期望: $expected_dir_arch)"
        return 1
    fi
}

# 执行测试
test_arch_conversion "amd64" "x86_64"
test_arch_conversion "arm64" "aarch64"
test_arch_conversion "unknown" "unknown"

echo ""
echo "=== 测试宿主机架构检测 ==="

# 测试宿主机架构检测
host_arch=$(detect_host_arch)
echo "检测到的宿主机架构: $host_arch"

# 验证检测结果
case $(uname -m) in
    "x86_64")
        expected="amd64"
        ;;
    "aarch64"|"arm64")
        expected="arm64"
        ;;
    *)
        expected="amd64"  # 默认值
        ;;
esac

if [ "$host_arch" = "$expected" ]; then
    echo "✓ 宿主机架构检测正确: $(uname -m) -> $host_arch"
else
    echo "✗ 宿主机架构检测错误: $(uname -m) -> $host_arch (期望: $expected)"
fi

echo ""
echo "=== 检查JDK安装包目录结构 ==="

JDK_BASE_DIR="${PROJECT_ROOT}/docker/infrastructure/jdk/shared/jdk"
VERSION_FILE="${SCRIPT_DIR}/versions/jdk_versions.json"

if [ ! -f "$VERSION_FILE" ]; then
    echo "错误: 版本配置文件不存在: $VERSION_FILE"
    exit 1
fi

# 获取支持的版本和架构
versions=($(jq -r '.versions | keys[]' "$VERSION_FILE"))
echo "支持的JDK版本: ${versions[*]}"

for version in "${versions[@]}"; do
    echo ""
    echo "检查JDK $version:"
    
    # 获取该版本的详细信息
    jdk_full_version=$(jq -r ".versions.\"$version\".jdk_version" "$VERSION_FILE")
    supported_archs=($(jq -r ".versions.\"$version\".supported_architectures[]" "$VERSION_FILE"))
    
    echo "  JDK完整版本: $jdk_full_version"
    echo "  支持的架构: ${supported_archs[*]}"
    
    # 检查每个架构的安装包
    for docker_arch in "${supported_archs[@]}"; do
        dir_arch=$(docker_arch_to_dir_arch "$docker_arch")
        jdk_package="${JDK_BASE_DIR}/${version}/${dir_arch}/jdk${jdk_full_version}.tar.gz"
        
        if [ -f "$jdk_package" ]; then
            echo "  ✓ $docker_arch ($dir_arch): $jdk_package"
        else
            echo "  ✗ $docker_arch ($dir_arch): $jdk_package (不存在)"
        fi
    done
done

echo ""
echo "=== 测试Dockerfile模板变量替换 ==="

TEMPLATE_FILE="${PROJECT_ROOT}/docker/infrastructure/jdk/Dockerfile.template"
if [ ! -f "$TEMPLATE_FILE" ]; then
    echo "错误: Dockerfile模板不存在: $TEMPLATE_FILE"
    exit 1
fi

echo "检查Dockerfile.template中的变量:"
grep -n '\${' "$TEMPLATE_FILE" | while read line; do
    echo "  $line"
done

echo ""
echo "=== 测试完成 ==="
echo "如果所有检查都通过，说明架构支持功能已正确实现"
