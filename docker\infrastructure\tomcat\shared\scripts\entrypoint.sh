#!/bin/bash
# Tomcat 容器启动脚本
# 确保环境变量正确设置

set -euo pipefail

# 检测 JAVA_HOME
detect_java_home() {
    local java_home=""
    
    # 尝试从环境变量获取
    if [ -n "${JAVA_HOME:-}" ] && [ -d "${JAVA_HOME:-}" ] && [ -f "${JAVA_HOME}/bin/java" ]; then
        java_home="$JAVA_HOME"
        echo "使用环境变量 JAVA_HOME: $java_home"
    # 尝试常见路径
    elif [ -d "/usr/local/jdk1.8.0_172" ] && [ -f "/usr/local/jdk1.8.0_172/bin/java" ]; then
        java_home="/usr/local/jdk1.8.0_172"
        echo "检测到 JDK 8: $java_home"
    elif [ -d "/usr/lib/jvm/java-8-openjdk-amd64" ] && [ -f "/usr/lib/jvm/java-8-openjdk-amd64/bin/java" ]; then
        java_home="/usr/lib/jvm/java-8-openjdk-amd64"
        echo "检测到 OpenJDK 8: $java_home"
    elif [ -d "/usr/lib/jvm/java-17-openjdk-amd64" ] && [ -f "/usr/lib/jvm/java-17-openjdk-amd64/bin/java" ]; then
        java_home="/usr/lib/jvm/java-17-openjdk-amd64"
        echo "检测到 OpenJDK 17: $java_home"
    else
        echo "错误: 无法找到有效的 Java 安装"
        exit 1
    fi
    
    echo "$java_home"
}

# 设置环境变量
setup_environment() {
    local java_home="$1"
    
    export JAVA_HOME="$java_home"
    export PATH="$java_home/bin:/usr/local/tomcat/bin:$PATH"
    
    echo "=== 环境变量设置 ==="
    echo "JAVA_HOME: $JAVA_HOME"
    echo "PATH: $PATH"
    echo "CATALINA_HOME: ${CATALINA_HOME:-/usr/local/tomcat}"
    echo "====================="
}

# 验证 Java 安装
verify_java() {
    echo "=== 验证 Java 安装 ==="
    
    if ! command -v java >/dev/null 2>&1; then
        echo "错误: java 命令未找到"
        exit 1
    fi
    
    echo "Java 版本信息:"
    java -version 2>&1
    
    echo "Java 可执行文件路径:"
    which java
    
    echo "✓ Java 验证通过"
    echo "====================="
}

# 验证 Tomcat 安装
verify_tomcat() {
    echo "=== 验证 Tomcat 安装 ==="
    
    local catalina_home="${CATALINA_HOME:-/usr/local/tomcat}"
    
    if [ ! -f "$catalina_home/bin/catalina.sh" ]; then
        echo "错误: Tomcat catalina.sh 脚本未找到"
        exit 1
    fi
    
    if [ ! -f "$catalina_home/bin/setclasspath.sh" ]; then
        echo "错误: Tomcat setclasspath.sh 脚本未找到"
        exit 1
    fi
    
    echo "测试 setclasspath.sh 脚本:"
    cd "$catalina_home"
    bash ./bin/setclasspath.sh || {
        echo "错误: setclasspath.sh 脚本执行失败"
        exit 1
    }
    
    echo "✓ Tomcat 验证通过"
    echo "====================="
}

# 主函数
main() {
    echo "=== Tomcat 容器启动 ==="
    
    # 检测并设置 JAVA_HOME
    local java_home
    java_home=$(detect_java_home)
    setup_environment "$java_home"
    
    # 验证安装
    verify_java
    verify_tomcat
    
    echo "=== 启动 Tomcat ==="
    
    # 如果有参数，执行参数指定的命令
    if [ $# -gt 0 ]; then
        echo "执行命令: $*"
        exec "$@"
    else
        # 默认启动 Tomcat
        echo "启动 Tomcat 服务器..."
        exec /usr/local/tomcat/bin/catalina.sh run
    fi
}

# 执行主函数
main "$@"
