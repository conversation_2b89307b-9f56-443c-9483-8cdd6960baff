#!/bin/bash

# JDK构建脚本架构选择示例
# 演示如何使用新的架构自动检测和手动指定功能

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_SCRIPT="${SCRIPT_DIR}/../build-jdk.sh"

echo "=== JDK构建脚本架构选择示例 ==="
echo ""

# 检查构建脚本是否存在
if [ ! -f "$BUILD_SCRIPT" ]; then
    echo "错误: 构建脚本不存在: $BUILD_SCRIPT"
    exit 1
fi

echo "构建脚本位置: $BUILD_SCRIPT"
echo ""

# 显示当前系统信息
echo "=== 系统信息 ==="
echo "操作系统: $(uname -s)"
echo "架构: $(uname -m)"
echo "内核版本: $(uname -r)"
echo ""

# 检测宿主机架构
source "$BUILD_SCRIPT"
host_arch=$(detect_host_arch)
echo "检测到的Docker架构: $host_arch"
echo ""

echo "=== 构建示例 ==="
echo ""

echo "1. 自动检测架构构建（推荐）"
echo "   命令: $BUILD_SCRIPT 8"
echo "   说明: 自动检测宿主机架构并构建对应的JDK 8镜像"
echo ""

echo "2. 指定单一架构构建"
echo "   命令: $BUILD_SCRIPT --arch=amd64 8"
echo "   说明: 强制构建AMD64架构的JDK 8镜像"
echo ""
echo "   命令: $BUILD_SCRIPT --arch=arm64 8"
echo "   说明: 强制构建ARM64架构的JDK 8镜像"
echo ""

echo "3. 构建所有支持的架构"
echo "   命令: $BUILD_SCRIPT --all-arch 8"
echo "   说明: 构建所有支持架构的JDK 8镜像"
echo ""

echo "4. 结合其他参数的示例"
echo "   命令: $BUILD_SCRIPT --arch=arm64 --debug 8"
echo "   说明: 构建ARM64架构的JDK 8镜像，启用调试模式"
echo ""
echo "   命令: $BUILD_SCRIPT --all-arch --output-dir=/tmp/dockerfiles 8"
echo "   说明: 构建所有架构的JDK 8镜像，并保存Dockerfile到指定目录"
echo ""

echo "=== 验证命令 ==="
echo ""

echo "1. 查看帮助信息"
echo "   命令: $BUILD_SCRIPT --help"
echo ""

echo "2. 测试架构支持"
echo "   命令: ${SCRIPT_DIR}/test-arch-support.sh"
echo ""

echo "3. 检查JDK安装包"
echo "   命令: find docker/infrastructure/jdk/shared/jdk -name '*.tar.gz' -type f"
echo ""

echo "=== 实际执行示例 ==="
echo ""

# 提供交互式选择
read -p "是否要执行自动检测架构的构建示例？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "执行: $BUILD_SCRIPT --help"
    echo "显示帮助信息..."
    "$BUILD_SCRIPT" --help
    echo ""
    
    echo "如果要实际构建，请运行:"
    echo "  $BUILD_SCRIPT 8"
    echo ""
    echo "注意: 实际构建需要:"
    echo "  1. 配置Harbor仓库地址"
    echo "  2. 确保JDK安装包存在于正确的目录中"
    echo "  3. Docker环境正常运行"
else
    echo "跳过实际执行"
fi

echo ""
echo "=== 架构选择优先级 ==="
echo "1. --arch=<架构> 参数（最高优先级）"
echo "2. --all-arch 参数"
echo "3. 宿主机架构自动检测（默认）"
echo ""

echo "=== 支持的架构 ==="
echo "- amd64 (x86_64): Intel/AMD 64位处理器"
echo "- arm64 (aarch64): ARM 64位处理器"
echo ""

echo "示例脚本执行完成！"
