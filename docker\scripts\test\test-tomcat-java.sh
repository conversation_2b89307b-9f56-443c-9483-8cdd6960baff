#!/bin/bash
# 测试 Tomcat 镜像中的 Java 环境
# 用于验证 JAVA_HOME 和相关脚本是否正常工作

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试镜像
IMAGE_NAME="${1:-**************:1443/btit/infra/tomcat:9.0.100-jdk8-btit-jammy}"

log_info "测试 Tomcat 镜像: $IMAGE_NAME"

# 测试 1: 基本的 Java 版本检查
log_info "测试 1: Java 版本检查"
if docker run --rm "$IMAGE_NAME" java -version; then
    log_info "✓ Java 版本检查成功"
else
    log_error "✗ Java 版本检查失败"
    exit 1
fi

echo ""

# 测试 2: JAVA_HOME 环境变量检查
log_info "测试 2: JAVA_HOME 环境变量检查"
if docker run --rm "$IMAGE_NAME" bash -c 'echo "JAVA_HOME: $JAVA_HOME" && test -n "$JAVA_HOME" && test -d "$JAVA_HOME"'; then
    log_info "✓ JAVA_HOME 环境变量正确设置"
else
    log_error "✗ JAVA_HOME 环境变量未正确设置"
    exit 1
fi

echo ""

# 测试 3: bash 环境中的 Java 检查
log_info "测试 3: bash 环境中的 Java 检查"
if docker run --rm "$IMAGE_NAME" bash -c 'source /etc/bash.bashrc && java -version'; then
    log_info "✓ bash 环境中 Java 正常"
else
    log_error "✗ bash 环境中 Java 异常"
    exit 1
fi

echo ""

# 测试 4: setclasspath.sh 脚本检查
log_info "测试 4: setclasspath.sh 脚本检查"
if docker run --rm "$IMAGE_NAME" bash -c 'cd /usr/local/tomcat && bash ./bin/setclasspath.sh'; then
    log_info "✓ setclasspath.sh 脚本执行成功"
else
    log_error "✗ setclasspath.sh 脚本执行失败"
    exit 1
fi

echo ""

# 测试 5: Tomcat 启动测试（快速启动和停止）
log_info "测试 5: Tomcat 启动测试"
if timeout 30 docker run --rm "$IMAGE_NAME" bash -c 'cd /usr/local/tomcat && timeout 10 ./bin/catalina.sh run || true'; then
    log_info "✓ Tomcat 启动测试完成"
else
    log_warn "⚠ Tomcat 启动测试可能有问题（这可能是正常的，因为我们只是快速测试）"
fi

echo ""

# 测试 6: 环境变量完整性检查
log_info "测试 6: 环境变量完整性检查"
docker run --rm "$IMAGE_NAME" bash -c '
echo "=== 环境变量检查 ==="
echo "JAVA_HOME: $JAVA_HOME"
echo "CATALINA_HOME: $CATALINA_HOME"
echo "PATH: $PATH"
echo "========================"

echo "=== Java 可执行文件检查 ==="
ls -la "$JAVA_HOME/bin/java" 2>/dev/null || echo "Java 可执行文件不存在"
which java || echo "java 命令未在 PATH 中找到"
echo "========================"

echo "=== Tomcat 关键文件检查 ==="
ls -la "$CATALINA_HOME/bin/catalina.sh" 2>/dev/null || echo "catalina.sh 不存在"
ls -la "$CATALINA_HOME/bin/setclasspath.sh" 2>/dev/null || echo "setclasspath.sh 不存在"
ls -la "$CATALINA_HOME/conf/server.xml" 2>/dev/null || echo "server.xml 不存在"
echo "========================"
'

echo ""
log_info "所有测试完成！"

# 测试 7: 交互式测试（可选）
read -p "是否要进行交互式测试？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "启动交互式容器，您可以手动测试..."
    log_info "在容器中可以尝试以下命令："
    log_info "  - java -version"
    log_info "  - echo \$JAVA_HOME"
    log_info "  - cd /usr/local/tomcat && bash ./bin/setclasspath.sh"
    log_info "  - ./bin/catalina.sh version"
    log_info "输入 'exit' 退出容器"
    docker run --rm -it "$IMAGE_NAME" bash
fi

log_info "测试脚本执行完成！"
